using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class MixerGameManager : MonoBehaviour
{
    [Header("Sliders")]
    public Slider[] sliders = new Slider[3];
    public TextMeshProUGUI[] sliderTexts = new TextMeshProUGUI[3];
    public RectTransform[] fluidRects = new RectTransform[3];

    [Header("Target")]
    private float[] targetPercents = new float[3];

    [Header("Mixed Output")]
    public RectTransform mixedFluidRect;

    [Header("Stability")]
    public RectTransform stabilityRect;
    public TextMeshProUGUI stabilityText;
    public Button mixButton;

    [Header("Settings")]
    public float maxHeight = 200f; // Max height for fluid UI

    [Header("Mix Effects")]
    public Transform shakeObject; // Object that will shake when mix button is clicked
    public GameObject[] objectsToActivate = new GameObject[2]; // Objects to activate
    public GameObject[] objectsToDeactivate = new GameObject[2]; // Objects to deactivate

    [Header("Shake Settings")]
    public float shakeDuration = 1f;
    public float shakeIntensity = 0.5f;

    void Start()
    {
        GenerateTargetMix();
        UpdateUI();
        foreach (var slider in sliders)
            slider.onValueChanged.AddListener(delegate { UpdateUI(); });

        // Add listener to mix button
        mixButton.onClick.AddListener(OnMixButtonClicked);
    }

    void GenerateTargetMix()
    {
        for (int i = 0; i < 3; i++)
        {
            targetPercents[i] = Random.Range(20f, 100f); // Or customize your range
        }

        mixButton.interactable = false;
        Debug.Log($"Target Mix: {targetPercents[0]}%, {targetPercents[1]}%, {targetPercents[2]}%");
    }

    void UpdateUI()
    {
        float total = 0f;
        float stabilitySum = 0f;

        for (int i = 0; i < 3; i++)
        {
            float value = sliders[i].value; // Value between 0 and 100
            total += value;

            // Update fluid level
            fluidRects[i].sizeDelta = new Vector2(fluidRects[i].sizeDelta.x, (value / 100f) * maxHeight);

            // Update text
            sliderTexts[i].text = Mathf.RoundToInt(value) + "%";

            // Compare with target
            float diff = Mathf.Abs(value - targetPercents[i]);
            float stability = Mathf.Clamp01(1f - (diff / 100f));
            stabilitySum += stability;
        }

        // Mixed Fluid: average height
        float avg = total / 3f;
        mixedFluidRect.sizeDelta = new Vector2(mixedFluidRect.sizeDelta.x, (avg / 100f) * maxHeight);

        // Stability: average of 3 stability scores
        float overallStability = stabilitySum / 3f;
        float stabilityHeight = overallStability * maxHeight;
        int stabilityPercentage = Mathf.RoundToInt(overallStability * 100f);

        // Update stability UI
        stabilityRect.sizeDelta = new Vector2(stabilityRect.sizeDelta.x, stabilityHeight);
        stabilityText.text = stabilityPercentage + "%";

        // Enable button if perfect
        mixButton.interactable = stabilityPercentage >= 100;
    }

    void OnMixButtonClicked()
    {
        // Start shaking animation
        if (shakeObject != null)
        {
            StartCoroutine(ShakeObject());
        }

        // Activate specified objects
        foreach (var obj in objectsToActivate)
        {
            if (obj != null)
                obj.SetActive(true);
        }

        // Deactivate specified objects
        foreach (var obj in objectsToDeactivate)
        {
            if (obj != null)
                obj.SetActive(false);
        }

        Debug.Log("Mix button clicked! Effects applied.");
    }

    IEnumerator ShakeObject()
    {
        if (shakeObject == null) yield break;

        Vector3 originalPosition = shakeObject.localPosition;
        float elapsed = 0f;

        while (elapsed < shakeDuration)
        {
            float x = Random.Range(-shakeIntensity, shakeIntensity);
            float y = Random.Range(-shakeIntensity, shakeIntensity);

            shakeObject.localPosition = originalPosition + new Vector3(x, y, 0);

            elapsed += Time.deltaTime;
            yield return null;
        }

        // Reset to original position
        shakeObject.localPosition = originalPosition;
    }
}
