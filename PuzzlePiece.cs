using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class PuzzlePiece : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBegin<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDragHandler, IPointerDownHandler
{
    public int pieceIndex; // Set by PuzzleManager when instantiating
    public GameObject correctPlacementEffect; // Object to activate when piece is placed correctly

    private Transform originalParent;
    private CanvasGroup canvasGroup;
    private RectTransform rectTransform;
    private ScrollRect parentScrollRect;
    private bool isDragging = false;
    private Vector2 startDragPosition;
    private float dragThreshold = 10f; // Minimum distance to start dragging
    private bool hasStartedDragging = false;

    void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
        rectTransform = GetComponent<RectTransform>();

        // Find the parent ScrollRect
        parentScrollRect = GetComponentInParent<ScrollRect>();
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        startDragPosition = eventData.position;
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        // Check if game has started
        if (PuzzleManager.Instance != null && !PuzzleManager.Instance.GameStarted)
        {
            return;
        }

        Vector2 dragDelta = eventData.position - startDragPosition;
        float dragDistance = dragDelta.magnitude;

        // Check if we've moved far enough to determine direction
        if (dragDistance < dragThreshold)
        {
            // Let the scroll rect handle this for now
            if (parentScrollRect != null)
            {
                parentScrollRect.OnBeginDrag(eventData);
            }
            return;
        }

        // Determine if movement is more vertical or horizontal
        float horizontalMovement = Mathf.Abs(dragDelta.x);
        float verticalMovement = Mathf.Abs(dragDelta.y);

        if (verticalMovement > horizontalMovement)
        {
            // Vertical movement - let scroll view handle it
            if (parentScrollRect != null)
            {
                parentScrollRect.OnBeginDrag(eventData);
            }
            return;
        }

        // Horizontal movement - start piece dragging
        isDragging = true;
        hasStartedDragging = true;
        originalParent = transform.parent;
        transform.SetParent(transform.root); // Bring to top of canvas
        canvasGroup.blocksRaycasts = false;

        // Stop scroll rect from interfering
        if (parentScrollRect != null)
        {
            parentScrollRect.enabled = false;
        }
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Check if game has started
        if (PuzzleManager.Instance != null && !PuzzleManager.Instance.GameStarted)
        {
            return;
        }

        // If we're not actually dragging the piece, let scroll rect handle it
        if (!isDragging)
        {
            if (parentScrollRect != null)
            {
                parentScrollRect.OnDrag(eventData);
            }
            return;
        }

        // Once we're dragging, allow movement in all directions (including up/down)
        rectTransform.anchoredPosition += eventData.delta;
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        // Check if game has started
        if (PuzzleManager.Instance != null && !PuzzleManager.Instance.GameStarted)
        {
            return;
        }

        // If we weren't actually dragging the piece, let scroll rect handle it
        if (!isDragging)
        {
            if (parentScrollRect != null)
            {
                parentScrollRect.OnEndDrag(eventData);
            }
            return;
        }

        // Re-enable scroll rect
        if (parentScrollRect != null)
        {
            parentScrollRect.enabled = true;
        }

        // Check for overlap with puzzle slots
        bool wasPlaced = CheckForSlotPlacement();

        // Only return to original parent if piece wasn't placed in a slot
        if (!wasPlaced)
        {
            transform.SetParent(originalParent);
        }

        canvasGroup.blocksRaycasts = true;
        isDragging = false;
        hasStartedDragging = false;
    }

    private bool CheckForSlotPlacement()
    {
        // Get all puzzle slots
        PuzzleSlot[] slots = FindObjectsOfType<PuzzleSlot>();

        foreach (PuzzleSlot slot in slots)
        {
            if (slot.correctIndex == pieceIndex)
            {
                // Check if this piece overlaps with the correct slot by at least 75%
                if (IsOverlapping(slot.GetComponent<RectTransform>(), 0.75f))
                {
                    // Snap to slot
                    transform.SetParent(slot.transform);
                    GetComponent<RectTransform>().anchoredPosition = Vector2.zero;
                    canvasGroup.blocksRaycasts = false;
                    enabled = false;

                    // Show correct placement effect
                    if (correctPlacementEffect != null)
                    {
                        StartCoroutine(ShowCorrectPlacementEffect());
                    }

                    PuzzleManager.Instance.RegisterCorrectPiece();
                    return true; // Piece was successfully placed
                }
            }
        }

        return false; // Piece was not placed in any slot
    }

    private bool IsOverlapping(RectTransform slotRect, float requiredOverlap)
    {
        RectTransform pieceRect = GetComponent<RectTransform>();

        // Get world corners
        Vector3[] pieceCorners = new Vector3[4];
        Vector3[] slotCorners = new Vector3[4];

        pieceRect.GetWorldCorners(pieceCorners);
        slotRect.GetWorldCorners(slotCorners);

        // Calculate bounds
        Rect pieceBounds = new Rect(
            pieceCorners[0].x, pieceCorners[0].y,
            pieceCorners[2].x - pieceCorners[0].x,
            pieceCorners[2].y - pieceCorners[0].y
        );

        Rect slotBounds = new Rect(
            slotCorners[0].x, slotCorners[0].y,
            slotCorners[2].x - slotCorners[0].x,
            slotCorners[2].y - slotCorners[0].y
        );

        // Calculate intersection
        float intersectionArea = Mathf.Max(0, Mathf.Min(pieceBounds.xMax, slotBounds.xMax) - Mathf.Max(pieceBounds.xMin, slotBounds.xMin)) *
                                Mathf.Max(0, Mathf.Min(pieceBounds.yMax, slotBounds.yMax) - Mathf.Max(pieceBounds.yMin, slotBounds.yMin));

        float pieceArea = pieceBounds.width * pieceBounds.height;

        return (intersectionArea / pieceArea) >= requiredOverlap;
    }

    private System.Collections.IEnumerator ShowCorrectPlacementEffect()
    {
        correctPlacementEffect.SetActive(true);
        yield return new WaitForSeconds(2f);
        correctPlacementEffect.SetActive(false);
    }
}
